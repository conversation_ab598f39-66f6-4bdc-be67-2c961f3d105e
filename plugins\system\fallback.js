export class fallback extends plugin {
  constructor() {
    super({
      name: "兜底响应机制",
      dsc: "当没有插件处理命令时的兜底响应",
      event: "message",
      priority: 10000, // 很低的优先级，但不是无限
      rule: [
        {
          reg: ".*", // 匹配所有消息
          fnc: "fallbackResponse",
          log: false,
        },
      ],
    })

    // plugins/example 插件的白名单命令正则表达式
    this.exampleWhitelist = [
      // 带#的命令
      /^#伤害总排行列表$/, // 伤害排行.js
      /^#伤害总排行列表4$/, // 伤害排行4.js
      /^#鸣潮共鸣链列表$/, // 共鸣链.js
      /^#鸣潮图鉴列表$/, // 图鉴.js
      /^#鸣潮天赋列表$/, // 天赋.js
      /^#希腊奶帮助$/, // 希腊奶.js
      /^#深塔信息$/, // 深塔信息.js
      /^#评分总排行列表$/, // 评分排行.js
      /^#评分总排行列表4$/, // 评分排行4.js
      /^#?(抽取|随机)?(今日|每日)?(Doro|doro)结局$/, // 每日doro结局.js
      /^#?(ww帮助|鸣潮帮助)$/, // 鸣潮帮助.js

      // 不带#的命令
      /^随机表情包列表$/, // 随机表情包按钮.js
      /^(\/帮助|菜单|help|帮助)$/, // 总菜单.js
    ]

    // 其他需要豁免的系统命令
    this.systemWhitelist = [
      /^#(状态|统计)/, // status.js
      /^#设置主人/, // master.js
      /^#(全局)?添加/, // add.js
      /^#(全局)?删除/, // add.js
      /^#(全局)?(消息|词条)/, // add.js
      /^#?撤回$/, // recallReply.js
      /^#重启$/, // restart.js
      /^#关机$/, // restart.js
      /^#停(机|止)$/, // restart.js
      /^#开机$/, // restart.js
      /^#版本$/, // version.js
      /^#安装/, // install.js
      /^#更新/, // 更新相关命令
      /^#日志/, // 日志相关命令
      /^#全部更新/, // 全部更新命令
      /^#更新日志/, // 更新日志命令
    ]

    // 标记是否已经有插件处理了当前消息
    this.messageProcessed = new Set()

    // ws-plugin检测缓存
    this.wsPluginCache = {
      exists: null,
      apps: null,
      lastCheck: 0,
    }

    // 记录等待处理的消息（用于检测是否有响应）
    this.pendingMessages = new Map()

    // 记录机器人回复的消息（用于检测gsuid等插件的响应）
    this.botReplies = new Map()

    // 记录用户消息的处理状态
    this.userMessages = new Map()

    // 监听机器人回复
    this.setupBotReplyListener()

    // 重写reply方法来捕获回复
    this.setupReplyInterceptor()
  }

  /**
   * accept方法，用于控制插件是否接受处理消息
   */
  async accept(e) {
    // 只允许QQBot使用
    if (!["QQBot"].includes(e.adapter_name)) {
      return false
    }

    // 处理所有消息
    if (!e.msg || !e.msg.trim()) {
      return false
    }

    return true
  }

  /**
   * 设置机器人回复监听器
   */
  setupBotReplyListener() {
    // 监听所有机器人发送的消息
    Bot.on("message", e => {
      // 检查是否是机器人自己发送的消息
      if (e.user_id === e.self_id) {
        const currentTime = Date.now()
        const groupId = e.group_id || "private"

        // 记录机器人在该群/私聊的回复时间
        const replyKey = `${e.self_id}_${groupId}`
        this.botReplies.set(replyKey, currentTime)

        console.log(`[兜底机制] 检测到机器人回复: ${replyKey} at ${currentTime}`)

        // 清理过期记录（保留最近30秒的记录）
        for (const [key, time] of this.botReplies.entries()) {
          if (currentTime - time > 30000) {
            this.botReplies.delete(key)
          }
        }
      }
    })
  }

  /**
   * 设置回复拦截器
   */
  setupReplyInterceptor() {
    // 拦截所有插件的reply方法
    const originalReply = plugin.prototype.reply
    if (originalReply) {
      plugin.prototype.reply = function (...args) {
        const currentTime = Date.now()

        // 记录回复事件
        if (this.e) {
          const groupId = this.e.group_id || "private"
          const selfId = this.e.self_id
          const replyKey = `${selfId}_${groupId}`

          // 获取兜底插件实例
          const fallbackInstance = global.fallbackInstance || this
          if (fallbackInstance && fallbackInstance.botReplies) {
            fallbackInstance.botReplies.set(replyKey, currentTime)
            console.log(`[兜底机制] reply拦截器检测到回复: ${replyKey} at ${currentTime}`)
          }
        }

        return originalReply.apply(this, args)
      }
    }

    // 保存实例引用
    global.fallbackInstance = this
  }

  async fallbackResponse(e) {
    // 只允许QQBot使用
    if (!["QQBot"].includes(e.adapter_name)) {
      return false
    }

    // 生成消息唯一标识（不包含时间戳，避免重复消息问题）
    const messageId = `${e.self_id}_${e.user_id}_${e.group_id || "private"}_${e.msg}`

    // 如果消息已经被处理过，不触发兜底响应
    if (this.messageProcessed.has(messageId)) {
      return false
    }

    // 检查消息是否为空
    if (!e.msg || !e.msg.trim()) {
      return false // 空消息，不触发兜底响应
    }

    // 检查是否是白名单命令（plugins/example）
    for (const regex of this.exampleWhitelist) {
      if (regex.test(e.msg)) {
        this.messageProcessed.add(messageId) // 标记为已处理
        return false // 不触发兜底响应
      }
    }

    // 检查是否是系统命令
    for (const regex of this.systemWhitelist) {
      if (regex.test(e.msg)) {
        this.messageProcessed.add(messageId) // 标记为已处理
        return false // 不触发兜底响应
      }
    }

    // 记录消息处理开始时间
    const messageStartTime = Date.now()
    const groupId = e.group_id || "private"
    const replyKey = `${e.self_id}_${groupId}`

    console.log(
      `[兜底机制] 开始处理消息: ${e.msg}, messageId: ${messageId}, replyKey: ${replyKey}, startTime: ${messageStartTime}`,
    )

    // 延迟执行，给插件充足的处理时间
    setTimeout(async () => {
      try {
        // 再次检查是否已被处理
        if (this.messageProcessed.has(messageId)) {
          console.log(`[兜底机制] 消息已被标记为处理: ${messageId}`)
          return
        }

        // 检查在消息发送后是否有机器人回复
        const lastReplyTime = this.botReplies.get(replyKey)
        console.log(
          `[兜底机制] 检查回复时间: replyKey=${replyKey}, lastReplyTime=${lastReplyTime}, messageStartTime=${messageStartTime}`,
        )

        if (lastReplyTime && lastReplyTime > messageStartTime) {
          // 有机器人回复，说明消息已被处理，不触发兜底
          console.log(`[兜底机制] 检测到回复，不触发兜底: ${messageId}`)
          this.messageProcessed.add(messageId)
          return
        }

        // 检查ws-plugin是否存在并可能处理消息
        const wsExists = await this.checkWsPluginExists()
        if (wsExists) {
          // 再次检查是否有回复（给gsuid更多时间）
          const currentReplyTime = this.botReplies.get(replyKey)
          console.log(`[兜底机制] ws-plugin存在，再次检查: currentReplyTime=${currentReplyTime}`)

          if (currentReplyTime && currentReplyTime > messageStartTime) {
            console.log(`[兜底机制] ws-plugin处理后检测到回复，不触发兜底: ${messageId}`)
            this.messageProcessed.add(messageId)
            return
          }
        }

        // 标记消息为已处理，避免重复触发
        this.messageProcessed.add(messageId)

        // 清理过期的消息记录（保留最近1000条）
        if (this.messageProcessed.size > 1000) {
          const entries = Array.from(this.messageProcessed)
          this.messageProcessed.clear()
          entries.slice(-500).forEach(id => this.messageProcessed.add(id))
        }

        console.log(`[兜底机制] 触发兜底响应: ${messageId}`)
        // 触发兜底响应
        await this.sendFallbackMessage(e)
      } catch (error) {
        console.error("兜底响应处理出错:", error)
      }
    }, 6000) // 减少到6秒，加快响应

    return false // 不阻止其他插件处理
  }

  /**
   * 检查是否有机器人回复
   */
  checkForBotReply(replyKey, messageStartTime) {
    const lastReplyTime = this.botReplies.get(replyKey)
    return lastReplyTime && lastReplyTime > messageStartTime
  }

  /**
   * 检查消息是否可能被ws-plugin处理
   */
  async mightBeProcessedByWSPlugin() {
    try {
      // 检查ws-plugin是否存在
      const wsExists = await this.checkWsPluginExists()
      if (!wsExists) {
        return false
      }

      // 如果ws-plugin存在，它可能会处理任何消息（通过message.js转发到gsuid_core）
      return true
    } catch (error) {
      console.error("检查ws-plugin处理可能性时出错:", error)
      return false
    }
  }

  /**
   * 检查ws-plugin是否存在
   */
  async checkWsPluginExists() {
    const now = Date.now()
    // 缓存5分钟
    if (this.wsPluginCache.lastCheck && now - this.wsPluginCache.lastCheck < 300000) {
      return this.wsPluginCache.exists
    }

    try {
      const wsPluginApps = await import("../ws-plugin/index.js").catch(() => null)
      this.wsPluginCache.exists = !!wsPluginApps
      this.wsPluginCache.apps = wsPluginApps?.apps || null
      this.wsPluginCache.lastCheck = now
      return this.wsPluginCache.exists
    } catch (error) {
      this.wsPluginCache.exists = false
      this.wsPluginCache.apps = null
      this.wsPluginCache.lastCheck = now
      return false
    }
  }

  /**
   * 检查ws-plugin是否已经处理完成了该消息
   */
  async checkWsPluginProcessed(e, messageStartTime) {
    try {
      // 首先检查ws-plugin是否存在
      const wsExists = await this.checkWsPluginExists()
      if (!wsExists) {
        return false // ws-plugin不存在，认为未处理
      }

      // 检查是否有机器人回复
      const groupId = e.group_id || "private"
      const replyKey = `${e.self_id}_${groupId}`

      return this.checkForBotReply(replyKey, messageStartTime)
    } catch (error) {
      console.error("检查ws-plugin处理状态时出错:", error)
      return false
    }
  }

  /**
   * 发送兜底响应消息
   */
  async sendFallbackMessage(e) {
    try {
      // 简化的提示信息
      const textContent = `\r#  未识别的命令\r\r可能为命令错误，请查看帮助\r\r点击下方按钮快速访问功能菜单`

      const params = [
        {
          key: "text",
          values: [textContent],
        },
      ]

      // 创建markdown segment
      let md = segment.markdown({
        custom_template_id: "102072241_1752245006", // 使用与总菜单相同的模板ID
        params: params,
      })

      // 创建按钮数据
      const buttonData = {
        type: "keyboard",
        id: "102072241_1751292952", // 使用与总菜单相同的按钮ID
      }

      // 组合markdown和按钮
      let msg = [md, segment.raw(buttonData)]

      setTimeout(async () => {
        try {
          await this.reply(msg, false, { at: false })
        } catch (error) {
          console.error("发送兜底响应消息出错：", error)
          // 如果markdown发送失败，回退到普通文本
          await e.reply(`🤖 未识别的命令\n\n可能为命令错误，请查看帮助`)
        }
      }, 1000)
    } catch (error) {
      console.error("发送兜底响应消息出错：", error)
      // 最终回退到简单文本回复
      await e.reply(`🤖 未识别的命令\n\n可能为命令错误，请查看帮助`)
    }
  }
}
