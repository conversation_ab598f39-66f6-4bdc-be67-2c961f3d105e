# 夏空
from ...api.model import RoleDetailData
from ...ascension.char import WavesCharResult, get_char_detail2
from ...damage.damage import DamageAttribute
from ...damage.utils import (
    SkillTreeMap,
    SkillType,
    cast_attack,
    cast_hit,
    cast_liberation,
    cast_skill,
    hit_damage,
    liberation_damage,
    skill_damage_calc,
)
from .damage import echo_damage, phase_damage, weapon_damage


def calc_damage_1(
    attr: DamageAttribute, role: RoleDetailData, isGroup: bool = False
) -> tuple[str, str]:
    attr.set_env_aero_erosion()
    # 设置角色伤害类型
    attr.set_char_damage(liberation_damage)
    # 设置角色模板  "temp_atk", "temp_life", "temp_def"
    attr.set_char_template("temp_atk")

    role_name = role.role.roleName
    # 获取角色详情
    char_result: WavesCharResult = get_char_detail2(role)

    skill_type: SkillType = "共鸣解放"
    # 获取角色技能等级
    skillLevel = role.get_skill_level(skill_type)
    # 技能技能倍率
    skill_multi = skill_damage_calc(
        char_result.skillTrees, SkillTreeMap[skill_type], "1", skillLevel
    )
    title = "即兴的交响诗"
    msg = f"技能倍率{skill_multi}"
    attr.add_skill_multi(skill_multi, title, msg)

    # 设置角色施放技能
    damage_func = [cast_attack, cast_skill, cast_hit, cast_liberation]
    phase_damage(attr, role, damage_func, isGroup)

    # 设置角色等级
    attr.set_character_level(role.role.level)

    # 设置角色固有技能
    role_breach = role.role.breach

    # 设置角色技能施放是不是也有加成 eg：守岸人
    title = "音律独奏"
    msg = "音律独奏状态时,使附近队伍中所有角色的气动伤害加成提升24%"
    attr.add_dmg_bonus(0.24, title, msg)

    # 设置声骸属性
    attr.set_phantom_dmg_bonus()

    # 设置共鸣链
    chain_num = role.get_chain_num()
    if chain_num >= 1:
        title = f"{role_name}-一链"
        msg = "施放普攻时，夏空的攻击提升35%"
        attr.add_atk_percent(0.35, title, msg)

    if chain_num >= 2:
        title = f"{role_name}-二链"
        msg = "三重华彩持续期间队伍中的角色气动伤害加成提升40%"
        attr.add_dmg_bonus(0.4, title, msg)

    if chain_num >= 4:
        title = f"{role_name}-四链"
        msg = "夏空造成共鸣解放伤害时无视敌人45%的防御。"
        attr.add_defense_reduction(0.45, title, msg)

    if chain_num >= 5:
        title = f"{role_name}-五链"
        msg = "夏空共鸣解放伤害加成提升40%"
        attr.add_dmg_bonus(0.4, title, msg)

    # 声骸
    echo_damage(attr, isGroup)

    # 武器
    weapon_damage(attr, role.weaponData, damage_func, isGroup)

    # 暴击伤害
    crit_damage = f"{attr.calculate_crit_damage():,.0f}"
    # 期望伤害
    expected_damage = f"{attr.calculate_expected_damage():,.0f}"
    return crit_damage, expected_damage


def calc_damage_2(
    attr: DamageAttribute, role: RoleDetailData, isGroup: bool = False
) -> tuple[str, str]:
    attr.set_env_aero_erosion()
    # 设置角色伤害类型
    attr.set_char_damage(hit_damage)
    # 设置角色模板  "temp_atk", "temp_life", "temp_def"
    attr.set_char_template("temp_atk")

    role_name = role.role.roleName
    # 获取角色详情
    char_result: WavesCharResult = get_char_detail2(role)

    skill_type: SkillType = "共鸣回路"
    # 获取角色技能等级
    skillLevel = role.get_skill_level(skill_type)
    # 技能技能倍率
    skill_multi = skill_damage_calc(
        char_result.skillTrees, SkillTreeMap[skill_type], "1", skillLevel
    )
    title = "重击·四拍重奏"
    msg = f"技能倍率{skill_multi}"
    attr.add_skill_multi(skill_multi, title, msg)

    # 设置角色施放技能
    damage_func = [cast_attack, cast_skill, cast_hit, cast_liberation]
    phase_damage(attr, role, damage_func, isGroup)

    # 设置角色等级
    attr.set_character_level(role.role.level)

    # 设置角色固有技能
    role_breach = role.role.breach
    if role_breach and role_breach >= 3:
        title = "固有技能"
        msg = "重击·四拍重奏造成的伤害提升30%。"
        attr.add_dmg_bonus(0.3, title, msg)

    # 设置角色技能施放是不是也有加成 eg：守岸人
    title = "音律独奏"
    msg = "音律独奏状态时,使附近队伍中所有角色的气动伤害加成提升24%"
    attr.add_dmg_bonus(0.24, title, msg)

    # 设置声骸属性
    attr.set_phantom_dmg_bonus()

    # 设置共鸣链
    chain_num = role.get_chain_num()
    if chain_num >= 1:
        title = f"{role_name}-一链"
        msg = "施放普攻时，夏空的攻击提升35%"
        attr.add_atk_percent(0.35, title, msg)

    if chain_num >= 4:
        title = f"{role_name}-四链"
        msg = "夏空重击四拍重奏造成伤害时无视敌人45%的防御。"
        attr.add_defense_reduction(0.45, title, msg)

    # 声骸
    echo_damage(attr, isGroup)

    # 武器
    weapon_damage(attr, role.weaponData, damage_func, isGroup)

    # 暴击伤害
    crit_damage = f"{attr.calculate_crit_damage():,.0f}"
    # 期望伤害
    expected_damage = f"{attr.calculate_expected_damage():,.0f}"
    return crit_damage, expected_damage


damage_detail = [
    {
        "title": "重击·四拍重奏",
        "func": lambda attr, role: calc_damage_2(attr, role),
    },
    {
        "title": "即兴的交响诗",
        "func": lambda attr, role: calc_damage_1(attr, role),
    },
]

rank = damage_detail[1]
